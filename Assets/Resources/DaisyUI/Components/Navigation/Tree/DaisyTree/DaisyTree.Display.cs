using UnityEngine.UIElements;

namespace BlastingDesign.UI.DaisyUI.Components.Navigation.Tree
{
    public partial class DaisyTree
    {
        #region Display Updates
        private void UpdateDisplay()
        {
            UpdateSelectionMode();
            UpdateLineDisplay();
            UpdateIconDisplay();
            UpdateActionDisplay();
            UpdateSearchDisplay();
            UpdateSearchPlaceholder();
            UpdateEmptyState();
        }

        private void UpdateSelectionMode()
        {
            if (_treeView != null)
            {
                _treeView.selectionType = MultiSelect ? SelectionType.Multiple : SelectionType.Single;
            }

            if (MultiSelect)
            {
                AddToClassList("multi-select");
                RemoveFromClassList("single-select");
            }
            else
            {
                RemoveFromClassList("multi-select");
                AddToClassList("single-select");
            }
        }

        private void UpdateLineDisplay()
        {
            if (ShowLines)
            {
                AddToClassList("show-lines");
                RemoveFromClassList("hide-lines");
            }
            else
            {
                RemoveFromClassList("show-lines");
                AddToClassList("hide-lines");
            }
        }

        private void UpdateIconDisplay()
        {
            if (ShowIcons)
            {
                AddToClassList("show-icons");
                RemoveFromClassList("hide-icons");
            }
            else
            {
                RemoveFromClassList("show-icons");
                AddToClassList("hide-icons");
            }
        }

        private void UpdateActionDisplay()
        {
            if (ShowActions)
            {
                AddToClassList("show-actions");
                RemoveFromClassList("hide-actions");
            }
            else
            {
                RemoveFromClassList("show-actions");
                AddToClassList("hide-actions");
            }
        }

        private void UpdateSearchDisplay()
        {
            if (_searchContainer != null)
            {
                _searchContainer.style.display = AllowSearch ? DisplayStyle.Flex : DisplayStyle.None;
            }
        }

        private void UpdateSearchPlaceholder()
        {
            if (_searchField != null)
            {
                _searchField.label = SearchPlaceholder;
            }
        }

        private void UpdateEmptyState()
        {
            bool isEmpty = TreeData.Count == 0;

            if (_emptyState != null)
            {
                _emptyState.style.display = isEmpty ? DisplayStyle.Flex : DisplayStyle.None;
            }

            if (_treeContainer != null)
            {
                _treeContainer.style.display = isEmpty ? DisplayStyle.None : DisplayStyle.Flex;
            }
        }
        #endregion
    }
}