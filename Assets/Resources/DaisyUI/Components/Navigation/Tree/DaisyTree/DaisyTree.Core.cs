using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.DaisyUI.Core;

namespace BlastingDesign.UI.DaisyUI.Components.Navigation.Tree
{
    public partial class DaisyTree
    {
        #region UXML Factory
        [UxmlAttribute]
        public string treeId { get; set; }

        [UxmlAttribute]
        public bool multiSelect { get; set; } = false;

        [UxmlAttribute]
        public bool showLines { get; set; } = true;

        [UxmlAttribute]
        public bool showIcons { get; set; } = true;

        [UxmlAttribute]
        public bool showActions { get; set; } = true;

        [UxmlAttribute]
        public bool allowSearch { get; set; } = false;

        [UxmlAttribute]
        public string searchPlaceholder { get; set; } = "Search...";
        #endregion

        #region Events
        public event Action<DaisyTreeData> OnItemClicked;
        public event Action<DaisyTreeData> OnItemSelected;
        public event Action<DaisyTreeData> OnItemDeselected;
        public event Action<DaisyTreeData> OnItemExpanded;
        public event Action<DaisyTreeData> OnItemCollapsed;
        public event Action<DaisyTreeData, string> OnActionTriggered;
        public event Action<string> OnSearchChanged;
        #endregion

        #region Properties
        public string TreeId
        {
            get => treeId;
            set
            {
                treeId = value;
                name = $"daisy-tree-{value}";
            }
        }

        public bool MultiSelect
        {
            get => multiSelect;
            set
            {
                multiSelect = value;
                UpdateSelectionMode();
            }
        }

        public bool ShowLines
        {
            get => showLines;
            set
            {
                showLines = value;
                UpdateLineDisplay();
            }
        }

        public bool ShowIcons
        {
            get => showIcons;
            set
            {
                showIcons = value;
                UpdateIconDisplay();
            }
        }

        public bool ShowActions
        {
            get => showActions;
            set
            {
                showActions = value;
                UpdateActionDisplay();
            }
        }

        public bool AllowSearch
        {
            get => allowSearch;
            set
            {
                allowSearch = value;
                UpdateSearchDisplay();
            }
        }

        public string SearchPlaceholder
        {
            get => searchPlaceholder;
            set
            {
                searchPlaceholder = value;
                UpdateSearchPlaceholder();
            }
        }

        public List<DaisyTreeData> TreeData { get; set; } = new List<DaisyTreeData>();
        public List<DaisyTreeData> FilteredData { get; set; } = new List<DaisyTreeData>();
        public string CurrentSearchQuery { get; set; } = string.Empty;
        #endregion

        #region UI Elements
        private VisualElement _container;
        private VisualElement _header;
        private VisualElement _searchContainer;
        private TextField _searchField;
        private Button _searchButton;
        private Button _clearSearchButton;
        private VisualElement _treeContainer;
        private CustomTreeView _treeView;
        private VisualElement _emptyState;
        private Label _emptyLabel;
        #endregion

        #region Template and Styling
        protected override string TemplatePath => "DaisyUI/Components/Navigation/Tree/DaisyTree";
        #endregion

        #region Constructors
        public DaisyTree() : base(DaisyUtilities.ComponentTypes.Tree)
        {
            ComponentType = "tree";
            InitializeComponent();
        }

        public DaisyTree(string id) : this()
        {
            TreeId = id;
        }

        public DaisyTree(List<DaisyTreeData> data) : this()
        {
            SetData(data);
        }
        #endregion

        #region Factory Methods
        public static DaisyTree Create(string id)
        {
            return new DaisyTree(id);
        }

        public static DaisyTree Create(List<DaisyTreeData> data)
        {
            return new DaisyTree(data);
        }
        #endregion

        #region Initialization
        private void InitializeComponent()
        {
            AddToClassList("daisy-tree");
        }

        protected override void OnInitialize()
        {
            base.OnInitialize();
            CacheElements();
            SetupTreeView();
            UpdateDisplay();
        }

        protected override void SetupEventHandlers()
        {
            base.SetupEventHandlers();

            if (_searchField != null)
            {
                _searchField.RegisterValueChangedCallback(OnSearchValueChanged);
                _searchField.RegisterCallback<KeyDownEvent>(OnSearchKeyDown);
            }

            if (_searchButton != null)
            {
                _searchButton.clicked += OnSearchButtonClicked;
            }

            if (_clearSearchButton != null)
            {
                _clearSearchButton.clicked += OnClearSearchButtonClicked;
            }
        }

        private void CacheElements()
        {
            _container = this.Q("container");
            _header = this.Q("header");
            _searchContainer = this.Q("search-container");
            _searchField = this.Q<TextField>("search-field");
            _searchButton = this.Q<Button>("search-button");
            _clearSearchButton = this.Q<Button>("clear-search-button");
            _treeContainer = this.Q("tree-container");
            var originalTreeView = this.Q<TreeView>("tree-view");
            if (originalTreeView != null)
            {
                // 替换原来的TreeView为自定义TreeView
                var parent = originalTreeView.parent;
                parent.Remove(originalTreeView);

                _treeView = new CustomTreeView();
                _treeView.name = "tree-view";
                _treeView.AddToClassList("daisy-tree-view");
                parent.Add(_treeView);
            }
            _emptyState = this.Q("empty-state");
            _emptyLabel = this.Q<Label>("empty-label");
        }

        private void SetupTreeView()
        {
            if (_treeView == null) return;

            // 设置TreeView的基本配置
            _treeView.selectionType = MultiSelect ? SelectionType.Multiple : SelectionType.Single;
            _treeView.reorderable = false;

            // 设置项目渲染
            _treeView.makeItem = MakeTreeItem;
            _treeView.bindItem = BindTreeItem;

            // 设置事件处理
            _treeView.selectionChanged += OnTreeViewSelectionChanged;
            _treeView.itemsChosen += OnTreeViewItemsChosen;
            _treeView.itemsSourceChanged += OnTreeViewItemsSourceChanged;

            // 设置自定义点击事件
            _treeView.OnItemSingleClicked += OnItemSingleClicked;
            _treeView.OnItemDoubleClicked += OnItemDoubleClicked;
        }

        #endregion

        #region TreeView Item Creation and Binding
        private VisualElement MakeTreeItem()
        {
            var container = new VisualElement();
            container.AddToClassList("daisy-tree-item");

            var content = new VisualElement();
            content.AddToClassList("daisy-tree-item-content");
            content.style.flexDirection = FlexDirection.Row;
            content.style.alignItems = Align.Center;

            // 图标
            var icon = new Label();
            icon.name = "icon";
            icon.AddToClassList("daisy-tree-item-icon");
            content.Add(icon);

            // 文本
            var text = new Label();
            text.name = "text";
            text.AddToClassList("daisy-tree-item-text");
            text.style.flexGrow = 1;
            content.Add(text);

            // 操作按钮容器
            var actionsContainer = new VisualElement();
            actionsContainer.name = "actions-container";
            actionsContainer.AddToClassList("daisy-tree-item-actions");
            actionsContainer.style.flexDirection = FlexDirection.Row;
            content.Add(actionsContainer);

            // 点击事件现在在TreeView级别处理

            container.Add(content);
            return container;
        }

        private void BindTreeItem(VisualElement element, int index)
        {
            var item = _treeView.GetItemDataForIndex<DaisyTreeData>(index);
            if (item == null) return;

            // 将数据存储到元素中，以便在点击事件中访问
            element.userData = item;

            var icon = element.Q<Label>("icon");
            var text = element.Q<Label>("text");
            var actionsContainer = element.Q("actions-container");

            // 设置图标
            if (icon != null && ShowIcons)
            {
                icon.text = item.Icon ?? "";
                icon.style.display = string.IsNullOrEmpty(item.Icon) ? DisplayStyle.None : DisplayStyle.Flex;
            }

            // 设置文本
            if (text != null)
            {
                text.text = item.Text ?? "";
            }

            // 设置操作按钮
            if (actionsContainer != null && ShowActions)
            {
                actionsContainer.Clear();
                foreach (var action in item.Actions)
                {
                    var actionButton = new Button(() => OnActionTriggered?.Invoke(item, action.Id))
                    {
                        text = action.Icon ?? action.Text
                    };
                    actionButton.AddToClassList("daisy-tree-action-button");
                    actionButton.tooltip = action.Text;
                    actionsContainer.Add(actionButton);
                }
                actionsContainer.style.display = item.Actions.Count > 0 ? DisplayStyle.Flex : DisplayStyle.None;
            }

            // 添加项目特定的类
            element.EnableInClassList("daisy-tree-item-has-children", item.HasChildren);
            element.EnableInClassList("daisy-tree-item-expanded", item.IsExpanded);
            element.EnableInClassList("daisy-tree-item-selected", item.IsSelected);
        }

        private void OnItemSingleClicked(object itemData)
        {
            if (itemData is DaisyTreeData item)
            {
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                Debug.Log($"[DaisyTree] Single clicked item: {item.Id}");
#endif

                // 触发项目点击事件
                OnItemClicked?.Invoke(item);

                // 单击仅选中项目，不展开/折叠
                var itemId = item.Id.GetHashCode();
                if (MultiSelect)
                {
                    // 多选模式下，切换选中状态
                    var selectedItems = _treeView.selectedItems.ToList();
                    if (selectedItems.Contains(item))
                    {
                        selectedItems.Remove(item);
                    }
                    else
                    {
                        selectedItems.Add(item);
                    }
                    _treeView.SetSelection(selectedItems.Select(item => ((DaisyTreeData)item).Id.GetHashCode()));
                }
                else
                {
                    // 单选模式下，直接选中
                    _treeView.SetSelection(new[] { item.Id.GetHashCode() });
                }
            }
        }

        private void OnItemDoubleClicked(object itemData)
        {
            if (itemData is DaisyTreeData item)
            {
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                Debug.Log($"[DaisyTree] Double clicked item: {item.Id}");
#endif

                // 双击展开/折叠
                if (item.HasChildren)
                {
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                    Debug.Log($"[DaisyTree] Toggling from double click: {item.Id}");
#endif
                    ToggleItem(item.Id);
                }
            }
        }

        #endregion

        #region TreeView Event Handlers
        private void OnTreeViewSelectionChanged(IEnumerable<object> selectedItems)
        {
            // Unity TreeView会自动管理选中状态的视觉样式
            // 我们只需要同步数据层的选中状态

            // 首先清除数据层的所有选中状态
            ClearAllSelectionData();

            // 然后设置新选中的项目数据状态
            foreach (var item in selectedItems)
            {
                if (item is DaisyTreeData treeData)
                {
                    treeData.IsSelected = true;
                    OnItemSelected?.Invoke(treeData);
                }
            }
        }

        private void ClearAllSelectionData()
        {
            // 只清除数据状态，不触发UI重建
            ClearSelectionRecursive(TreeData);
        }

        private void ClearSelectionRecursive(IEnumerable<DaisyTreeData> items)
        {
            if (items == null) return;

            foreach (var item in items)
            {
                item.IsSelected = false;
                if (item.Children != null && item.Children.Count > 0)
                {
                    ClearSelectionRecursive(item.Children);
                }
            }
        }

        private void OnTreeViewItemsChosen(IEnumerable<object> chosenItems)
        {
            foreach (var item in chosenItems)
            {
                if (item is DaisyTreeData treeData)
                {
                    OnItemClicked?.Invoke(treeData);
                }
            }
        }


        private void OnTreeViewItemsSourceChanged()
        {
            UpdateEmptyState();
        }

        #endregion

        #region Data Management
        public DaisyTree SetData(List<DaisyTreeData> data)
        {
            TreeData = data ?? new List<DaisyTreeData>();
            FilteredData = new List<DaisyTreeData>(TreeData);

            if (_treeView != null)
            {
                var itemDataList = FilteredData.ConvertAll(item => CreateTreeViewItemData(item));
                _treeView.SetRootItems(itemDataList);
                _treeView.Rebuild();

                // 设置初始展开状态
                SetInitialExpandState(TreeData);
            }

            UpdateEmptyState();
            return this;
        }

        private TreeViewItemData<DaisyTreeData> CreateTreeViewItemData(DaisyTreeData data)
        {
            var children = new List<TreeViewItemData<DaisyTreeData>>();

            // 递归创建子项
            foreach (var child in data.Children)
            {
                children.Add(CreateTreeViewItemData(child));
            }

            return new TreeViewItemData<DaisyTreeData>(data.Id.GetHashCode(), data, children);
        }

        private void SetInitialExpandState(List<DaisyTreeData> items)
        {
            foreach (var item in items)
            {
                if (item.HasChildren)
                {
                    var itemId = item.Id.GetHashCode();
                    if (item.IsExpanded)
                    {
                        _treeView.ExpandItem(itemId);
                    }
                    else
                    {
                        _treeView.CollapseItem(itemId);
                    }

                    // 递归处理子项
                    SetInitialExpandState(item.Children);
                }
            }
        }

        public DaisyTree AddItem(DaisyTreeData item)
        {
            if (item == null) return this;

            TreeData.Add(item);
            FilterData();
            return this;
        }

        public DaisyTree RemoveItem(string id)
        {
            var item = FindItem(id);
            if (item != null)
            {
                TreeData.Remove(item);
                FilterData();
            }
            return this;
        }

        public DaisyTreeData FindItem(string id)
        {
            return FindItemRecursive(TreeData, id);
        }

        private DaisyTreeData FindItemRecursive(List<DaisyTreeData> items, string id)
        {
            foreach (var item in items)
            {
                if (item.Id == id) return item;
                if (item.Children != null)
                {
                    var found = FindItemRecursive(item.Children, id);
                    if (found != null) return found;
                }
            }
            return null;
        }
        #endregion

        #region Utility Methods
        public override void Reset()
        {
            base.Reset();

            TreeId = null;
            MultiSelect = false;
            ShowLines = true;
            ShowIcons = true;
            ShowActions = true;
            AllowSearch = false;
            SearchPlaceholder = "Search...";
            CurrentSearchQuery = string.Empty;

            TreeData.Clear();
            FilteredData.Clear();

            OnItemClicked = null;
            OnItemSelected = null;
            OnItemDeselected = null;
            OnItemExpanded = null;
            OnItemCollapsed = null;
            OnActionTriggered = null;
            OnSearchChanged = null;
        }

        public override string ToString()
        {
            return $"DaisyTree(Id: {TreeId}, Items: {TreeData.Count})";
        }
        #endregion
    }
}
