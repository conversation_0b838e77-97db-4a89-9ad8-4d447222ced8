/* ===== DaisyTree主容器样式 (基于TreeView) ===== */
.daisy-tree {
    flex-direction: column;
    background-color: var(--daisy-base-100);
    border-radius: var(--daisy-rounded-md);
    border-width: 1px;
    border-color: var(--daisy-base-300);
    overflow: hidden;
    min-height: 200px;
}

.daisy-tree-container {
    flex: 1;
    flex-direction: column;
}

/* ===== 头部区域 ===== */
.daisy-tree-header {
    flex-direction: column;
    background-color: var(--daisy-base-200);
    border-bottom-width: 1px;
    border-color: var(--daisy-base-300);
}

/* ===== 搜索区域 ===== */
.daisy-tree-search-container {
    flex-direction: row;
    align-items: center;
    padding: 8px;
    background-color: var(--daisy-base-200);
    display: none; /* 默认隐藏，通过AllowSearch控制 */
}

.daisy-tree-search-field {
    flex: 1;
    margin-right: 8px;
    background-color: var(--daisy-base-100);
    border-width: 1px;
    border-color: var(--daisy-base-300);
    border-radius: var(--daisy-rounded-sm);
    padding: 4px 8px;
    color: var(--daisy-base-content);
}

.daisy-tree-search-field:focus {
    border-color: var(--daisy-primary);
}

.daisy-tree-search-button,
.daisy-tree-clear-search-button {
    width: 28px;
    height: 28px;
    background-color: var(--daisy-base-300);
    border-width: 1px;
    border-color: var(--daisy-base-400);
    border-radius: var(--daisy-rounded-sm);
    color: var(--daisy-base-content);
    margin-left: 4px;
    justify-content: center;
    align-items: center;
}

.daisy-tree-search-button:hover,
.daisy-tree-clear-search-button:hover {
    background-color: var(--daisy-base-200);
}

/* ===== 树容器 ===== */
.daisy-tree-content {
    flex: 1;
    background-color: var(--daisy-base-100);
}

/* ===== TreeView样式 ===== */
.daisy-tree-view {
    flex: 1;
    background-color: var(--daisy-base-100);
}

/* Unity TreeView 内置样式覆盖 */
.daisy-tree-view .unity-tree-view__item {
    padding: 0;
    margin: 0;
    border-width: 0px;
}

.daisy-tree-view .unity-tree-view__item:hover {
    background-color: var(--daisy-base-200);
}

/* Unity TreeView 选中状态样式 */
.daisy-tree-view .unity-tree-view__item--selected {
    background-color: var(--daisy-primary);
    color: var(--daisy-primary-content);
}

.daisy-tree-view .unity-tree-view__item--selected:hover {
    background-color: var(--daisy-primary-focus);
}

/* 确保选中状态的树项内容也应用正确的颜色 */
.daisy-tree-view .unity-tree-view__item--selected .daisy-tree-item-icon,
.daisy-tree-view .unity-tree-view__item--selected .daisy-tree-item-text {
    color: var(--daisy-primary-content);
}

.daisy-tree-view .unity-tree-view__item--selected .daisy-tree-action-button {
    color: var(--daisy-primary-content);
}

.daisy-tree-view .unity-tree-view__item--selected .daisy-tree-action-button:hover {
    background-color: var(--daisy-primary-focus);
}

.daisy-tree-view .unity-tree-view__item-toggle {
    width: auto;
    height: auto;
    border-width: 0;
    background-color: transparent;
    color: var(--daisy-base-content);
    font-size: 12px;
    margin-right: 4px;
}

.daisy-tree-view .unity-tree-view__item-toggle:hover {
    background-color: var(--daisy-base-300);
    border-radius: var(--daisy-rounded-sm);
}

/* ===== 树项样式 ===== */
.daisy-tree-item {
    flex-direction: row;
    align-items: center;

    background-color: transparent;
    border-width: 0;
}

.daisy-tree-item:hover {
    background-color: var(--daisy-base-200);
}

.daisy-tree-item-selected {
    background-color: var(--daisy-primary);
    color: var(--daisy-primary-content);
}

.daisy-tree-item-selected:hover {
    background-color: var(--daisy-primary-focus);
}

.daisy-tree-item-content {
    flex: 1;
    flex-direction: row;
    align-items: center;
    min-height: 24px;
}

/* ===== 图标样式 ===== */
.daisy-tree-item-icon {
    width: 16px;
    height: 16px;
    margin-right: 6px;
    color: var(--daisy-base-content);
    flex-shrink: 0;
    -unity-text-align: middle-center;
    font-size: 14px;
}

.daisy-tree-item-selected .daisy-tree-item-icon {
    color: var(--daisy-primary-content);
}

/* ===== 文本样式 ===== */
.daisy-tree-item-text {
    flex: 1;
    color: var(--daisy-base-content);
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    -unity-text-align: middle-left;
}

.daisy-tree-item-selected .daisy-tree-item-text {
    color: var(--daisy-primary-content);
}

/* ===== 操作按钮样式 ===== */
.daisy-tree-item-actions {
    flex-direction: row;
    align-items: center;
    margin-left: 8px;
    flex-shrink: 0;
}

.daisy-tree-action-button {
    width: 24px;
    height: 24px;
    background-color: transparent;
    border-width: 1px;
    border-color: transparent;
    border-radius: var(--daisy-rounded-sm);
    margin-left: 2px;
    align-items: center;
    justify-content: center;
    color: var(--daisy-base-content);
    opacity: 0.7;
    font-size: 12px;
    transition: all 0.2s ease;
}

.daisy-tree-action-button:hover {
    background-color: var(--daisy-base-300);
    border-color: var(--daisy-base-400);
    opacity: 1;
}

.daisy-tree-item-selected .daisy-tree-action-button {
    color: var(--daisy-primary-content);
}

.daisy-tree-item-selected .daisy-tree-action-button:hover {
    background-color: var(--daisy-primary-focus);
}

/* ===== 状态样式 ===== */
.daisy-tree-item-has-children {
    /* 有子项的样式 */
}

.daisy-tree-item-expanded {
    /* 已展开的样式 */
}

/* ===== 空状态 ===== */
.daisy-tree-empty-state {
    flex: 1;
    align-items: center;
    justify-content: center;
    padding: 40px;
    background-color: var(--daisy-base-100);
    display: none; /* 默认隐藏 */
}

.daisy-tree-empty-label {
    color: var(--daisy-base-content);
    font-size: 14px;
    opacity: 0.6;
    -unity-text-align: middle-center;
}

/* ===== 主题变体 ===== */
.daisy-tree.dark {
    background-color: #2a2a2a;
    border-color: #404040;
}

.daisy-tree.dark .daisy-tree-header,
.daisy-tree.dark .daisy-tree-search-container {
    background-color: #2a2a2a;
    border-color: #404040;
}

.daisy-tree.dark .daisy-tree-content,
.daisy-tree.dark .daisy-tree-view,
.daisy-tree.dark .daisy-tree-empty-state {
    background-color: #1f1f1f;
}

.daisy-tree.dark .daisy-tree-search-field {
    background-color: #1f1f1f;
    border-color: #404040;
    color: #ffffff;
}

.daisy-tree.dark .daisy-tree-search-button,
.daisy-tree.dark .daisy-tree-clear-search-button {
    background-color: #404040;
    border-color: #505050;
    color: #ffffff;
}

.daisy-tree.dark .daisy-tree-item:hover,
.daisy-tree.dark .unity-tree-view__item:hover {
    background-color: #404040;
}

/* 深色主题选中状态样式 */
.daisy-tree.dark .unity-tree-view__item--selected {
    background-color: var(--daisy-primary);
    color: var(--daisy-primary-content);
}

.daisy-tree.dark .unity-tree-view__item--selected:hover {
    background-color: var(--daisy-primary-focus);
}

.daisy-tree.dark .unity-tree-view__item--selected .daisy-tree-item-icon,
.daisy-tree.dark .unity-tree-view__item--selected .daisy-tree-item-text {
    color: var(--daisy-primary-content);
}

.daisy-tree.dark .unity-tree-view__item--selected .daisy-tree-action-button {
    color: var(--daisy-primary-content);
}

.daisy-tree.dark .unity-tree-view__item--selected .daisy-tree-action-button:hover {
    background-color: var(--daisy-primary-focus);
}

.daisy-tree.dark .daisy-tree-item-selected {
    background-color: var(--daisy-primary);
    color: var(--daisy-primary-content);
}

.daisy-tree.dark .daisy-tree-item-selected:hover {
    background-color: var(--daisy-primary-focus);
}

.daisy-tree.dark .daisy-tree-item-selected .daisy-tree-item-icon,
.daisy-tree.dark .daisy-tree-item-selected .daisy-tree-item-text {
    color: var(--daisy-primary-content);
}

.daisy-tree.dark .daisy-tree-item-selected .daisy-tree-action-button {
    color: var(--daisy-primary-content);
}

.daisy-tree.dark .daisy-tree-item-selected .daisy-tree-action-button:hover {
    background-color: var(--daisy-primary-focus);
}

.daisy-tree.dark .daisy-tree-item-icon,
.daisy-tree.dark .daisy-tree-item-text {
    color: #ffffff;
}

.daisy-tree.dark .daisy-tree-action-button {
    color: #ffffff;
}

.daisy-tree.dark .daisy-tree-action-button:hover {
    background-color: #505050;
}

.daisy-tree.dark .daisy-tree-empty-label {
    color: #ffffff;
}

/* ===== 尺寸变体 ===== */
.daisy-tree.compact .daisy-tree-item {
    min-height: 24px;
    padding: 2px 4px;
}

.daisy-tree.compact .daisy-tree-item-text {
    font-size: 12px;
}

.daisy-tree.compact .daisy-tree-action-button {
    width: 20px;
    height: 20px;
}

.daisy-tree.lg .daisy-tree-item {
    min-height: 36px;
    padding: 6px 12px;
}

.daisy-tree.lg .daisy-tree-item-text {
    font-size: 15px;
}

.daisy-tree.lg .daisy-tree-action-button {
    width: 28px;
    height: 28px;
}

/* ===== 显示选项 ===== */
.daisy-tree.hide-icons .daisy-tree-item-icon {
    display: none;
}

.daisy-tree.hide-actions .daisy-tree-item-actions {
    display: none;
}

.daisy-tree.show-lines .unity-tree-view__item {
    border-left-width: 1px;
    border-left-color: var(--daisy-base-300);
}

/* ===== 选择模式 ===== */
.daisy-tree.multi-select .unity-tree-view__item--selected {
    background-color: var(--daisy-primary);
    color: var(--daisy-primary-content);
}

.daisy-tree.single-select .unity-tree-view__item--selected {
    background-color: var(--daisy-primary);
    color: var(--daisy-primary-content);
}


