/* ===== DaisyTreeNode节点样式 ===== */
.daisy-tree-node {
    flex-direction: column;
    width: 100%;
    margin: 0;
    padding: 0;
}

.daisy-tree-node #container {
    flex-direction: column;
}

/* ===== 节点头部 ===== */
.daisy-tree-node #header {
    flex-direction: row;
    align-items: center;
    min-height: 32px;
    padding: 4px 8px;
    background-color: transparent;
    border-width: 1px;
    position: relative;
}

.daisy-tree-node #header:hover {
    background-color: var(--daisy-base-200);
}

.daisy-tree-node.selected #header {
    background-color: var(--daisy-primary);
    color: var(--daisy-primary-content);
}

.daisy-tree-node.selected #header:hover {
    background-color: var(--daisy-primary-focus);
}

.daisy-tree-node.focus #header {
    background-color: var(--daisy-base-300);
}

/* ===== 缩进容器 ===== */
.daisy-tree-node #indent-container {
    flex-direction: row;
    align-items: center;
    flex-shrink: 0;
}

.daisy-tree-node .tree-indent {
    width: 20px;
    height: 24px;
    flex-shrink: 0;
    position: relative;
}

/* ===== 展开按钮 ===== */
.daisy-tree-node #expand-button {
    width: 20px;
    height: 20px;
    background-color: transparent;
    border-width: 1px;
    border-radius: var(--daisy-rounded-sm);
    margin-right: 4px;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.daisy-tree-node #expand-button:hover {
    background-color: var(--daisy-base-300);
}

.daisy-tree-node #expand-icon {
    font-size: 12px;
    color: var(--daisy-base-content);
    transition: transform 0.2s ease;
}

.daisy-tree-node #expand-icon.expanded {
    rotate: 90deg;
}

.daisy-tree-node #expand-icon.collapsed {
    rotate: 0deg;
}

/* ===== 节点内容 ===== */
.daisy-tree-node #content {
    flex: 1;
    flex-direction: row;
    align-items: center;
    min-height: 24px;
}

.daisy-tree-node #main-button {
    flex: 1;
    background-color: transparent;
    border-width: 1px;
    -unity-text-align: middle-left;
    flex-direction: row;
    align-items: center;
    min-height: 24px;
    padding: 0;
}

.daisy-tree-node #main-button:hover {
    background-color: transparent;
}

/* ===== 图标 ===== */
.daisy-tree-node #icon {
    width: 16px;
    height: 16px;
    margin-right: 6px;
    color: var(--daisy-base-content);
    flex-shrink: 0;
    -unity-text-align: middle-center;
}

.daisy-tree-node.selected #icon {
    color: var(--daisy-primary-content);
}

/* ===== 文本 ===== */
.daisy-tree-node #text {
    flex: 1;
    color: var(--daisy-base-content);
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    -unity-text-align: middle-left;
}

.daisy-tree-node.selected #text {
    color: var(--daisy-primary-content);
}

/* ===== 操作按钮容器 ===== */
.daisy-tree-node #actions-container {
    flex-direction: row;
    align-items: center;
    margin-left: 8px;
    flex-shrink: 0;
}

.daisy-tree-node .tree-action-button {
    width: 24px;
    height: 24px;
    background-color: transparent;
    border-width: 1px;
    border-radius: var(--daisy-rounded-sm);
    margin-left: 2px;
    align-items: center;
    justify-content: center;
    color: var(--daisy-base-content);
    opacity: 0.7;
    transition: all 0.2s ease;
}

.daisy-tree-node .tree-action-button:hover {
    background-color: var(--daisy-base-300);
    opacity: 1;
}

.daisy-tree-node.selected .tree-action-button {
    color: var(--daisy-primary-content);
}

.daisy-tree-node.selected .tree-action-button:hover {
    background-color: var(--daisy-primary-focus);
}

/* ===== 子节点容器 ===== */
.daisy-tree-node #children-container {
    flex-direction: column;
    margin-left: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.daisy-tree-node.expanded #children-container {
    display: flex;
    max-height: none;
}

.daisy-tree-node.collapsed #children-container {
    display: none;
    max-height: 0;
}

/* ===== 层级深度样式 ===== */
.daisy-tree-node.depth-0 #indent-container {
    margin-left: 0;
}

.daisy-tree-node.depth-1 #indent-container {
    margin-left: 20px;
}

.daisy-tree-node.depth-2 #indent-container {
    margin-left: 40px;
}

.daisy-tree-node.depth-3 #indent-container {
    margin-left: 60px;
}

.daisy-tree-node.depth-4 #indent-container {
    margin-left: 80px;
}

.daisy-tree-node.depth-5 #indent-container {
    margin-left: 100px;
}

/* ===== 连接线样式 ===== */



/* ===== 深色主题 ===== */
.daisy-tree.dark .daisy-tree-node #header:hover {
    background-color: #404040;
}

.daisy-tree.dark .daisy-tree-node.selected #header {
    background-color: var(--daisy-primary);
}

.daisy-tree.dark .daisy-tree-node.focus #header {
    background-color: #505050;
}

.daisy-tree.dark .daisy-tree-node #expand-button:hover {
    background-color: #505050;
}

.daisy-tree.dark .daisy-tree-node #expand-icon {
    color: #ffffff;
}

.daisy-tree.dark .daisy-tree-node #icon {
    color: #ffffff;
}

.daisy-tree.dark .daisy-tree-node #text {
    color: #ffffff;
}

.daisy-tree.dark .daisy-tree-node .tree-action-button {
    color: #ffffff;
}

.daisy-tree.dark .daisy-tree-node .tree-action-button:hover {
    background-color: #505050;
}


/* ===== 尺寸变体 ===== */
.daisy-tree.xs .daisy-tree-node #header {
    min-height: 24px;
    padding: 2px 4px;
}

.daisy-tree.xs .daisy-tree-node #text {
    font-size: 12px;
}

.daisy-tree.xs .daisy-tree-node .tree-action-button {
    width: 20px;
    height: 20px;
}

.daisy-tree.sm .daisy-tree-node #header {
    min-height: 28px;
    padding: 3px 6px;
}

.daisy-tree.sm .daisy-tree-node #text {
    font-size: 13px;
}

.daisy-tree.sm .daisy-tree-node .tree-action-button {
    width: 22px;
    height: 22px;
}

.daisy-tree.lg .daisy-tree-node #header {
    min-height: 36px;
    padding: 6px 12px;
}

.daisy-tree.lg .daisy-tree-node #text {
    font-size: 15px;
}

.daisy-tree.lg .daisy-tree-node .tree-action-button {
    width: 28px;
    height: 28px;
}

.daisy-tree.xl .daisy-tree-node #header {
    min-height: 40px;
    padding: 8px 16px;
}

.daisy-tree.xl .daisy-tree-node #text {
    font-size: 16px;
}

.daisy-tree.xl .daisy-tree-node .tree-action-button {
    width: 32px;
    height: 32px;
}

/* ===== 修饰符 ===== */
.daisy-tree.compact .daisy-tree-node #header {
    min-height: 24px;
    padding: 2px 4px;
}

.daisy-tree.compact .daisy-tree-node .tree-indent {
    width: 16px;
}

.daisy-tree.compact .daisy-tree-node #expand-button {
    width: 16px;
    height: 16px;
}

.daisy-tree.compact .daisy-tree-node .tree-action-button {
    width: 20px;
    height: 20px;
}

.daisy-tree.bordered .daisy-tree-node {
}

.daisy-tree.rounded .daisy-tree-node #header {
    border-radius: var(--daisy-rounded-md);
    margin: 1px;
}

.daisy-tree.shadow .daisy-tree-node.selected #header {
}

/* ===== 状态样式 ===== */
.daisy-tree-node.disabled {
    opacity: 0.5;
}

.daisy-tree-node.loading #icon {
}

@keyframes loading-spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.daisy-tree-node.highlighted #header {
    background-color: var(--daisy-accent);
    color: var(--daisy-accent-content);
}

.daisy-tree-node.highlighted #header:hover {
    background-color: var(--daisy-accent-focus);
}

/* ===== 动画效果 ===== */
.daisy-tree-node #header {
    transition: all 0.2s ease;
}

.daisy-tree-node #expand-icon {
    transition: transform 0.2s ease;
}

.daisy-tree-node #children-container {
    transition: all 0.3s ease;
}

.daisy-tree-node .tree-action-button {
    transition: all 0.2s ease;
}

/* ===== 拖拽状态 ===== */
.daisy-tree.drag-drop-enabled .daisy-tree-node.dragging {
    opacity: 0.5;
    scale: 0.98;
}

.daisy-tree.drag-drop-enabled .daisy-tree-node.drop-target #header {
    background-color: var(--daisy-accent);
    border-width: 2px;
}

.daisy-tree.drag-drop-enabled .daisy-tree-node.drop-hover #header {
    background-color: var(--daisy-accent-focus);
}

/* ===== 搜索高亮 ===== */
.daisy-tree-node.search-match #text {
    background-color: var(--daisy-warning);
    color: var(--daisy-warning-content);
    padding: 0 2px;
    border-radius: var(--daisy-rounded-sm);
}

.daisy-tree-node.search-highlight #header {
    background-color: var(--daisy-info);
    color: var(--daisy-info-content);
}

/* ===== 响应式调整 ===== */
@media (max-width: 768px) {
    .daisy-tree-node #header {
        min-height: 36px;
        padding: 6px 8px;
    }
    
    .daisy-tree-node .tree-action-button {
        width: 28px;
        height: 28px;
    }
    
    .daisy-tree-node #text {
        font-size: 15px;
    }
}

/* ===== 可访问性 ===== */
.daisy-tree-node #header:focus {
}

.daisy-tree-node.selected #header:focus {
}

/* ===== 打印样式 ===== */
@media print {
    .daisy-tree-node #header {
        background-color: transparent !important;
        color: black !important;
    }
    
    .daisy-tree-node .tree-action-button {
        display: none;
    }
}