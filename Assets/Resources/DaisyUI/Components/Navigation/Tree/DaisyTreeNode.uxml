<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements">
    <ui:VisualElement name="container" class="daisy-tree-node-container">
        <ui:VisualElement name="header" class="daisy-tree-node-header">
            <ui:VisualElement name="indent-container" class="daisy-tree-node-indent-container">
                <!-- 缩进元素将在这里动态添加 -->
            </ui:VisualElement>
            
            <ui:Button name="expand-button" class="daisy-tree-node-expand-button">
                <ui:Label name="expand-icon" text="▶" class="daisy-tree-node-expand-icon" />
            </ui:Button>
            
            <ui:VisualElement name="content" class="daisy-tree-node-content">
                <ui:Button name="main-button" class="daisy-tree-node-main-button">
                    <ui:Label name="icon" text="" class="daisy-tree-node-icon daisy-icon" />
                    <ui:Label name="text" text="Node" class="daisy-tree-node-text" />
                </ui:Button>
                
                <ui:VisualElement name="actions-container" class="daisy-tree-node-actions-container">
                    <!-- 操作按钮将在这里动态添加 -->
                </ui:VisualElement>
            </ui:VisualElement>
        </ui:VisualElement>
        
        <ui:VisualElement name="children-container" class="daisy-tree-node-children-container">
            <!-- 子节点和子项目将在这里动态添加 -->
        </ui:VisualElement>
    </ui:VisualElement>
</ui:UXML>