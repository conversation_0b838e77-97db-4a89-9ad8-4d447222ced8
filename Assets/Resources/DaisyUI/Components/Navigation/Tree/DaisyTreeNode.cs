using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.DaisyUI.Core;

namespace BlastingDesign.UI.DaisyUI.Components.Navigation.Tree
{
    [UxmlElement]
    public partial class DaisyTreeNode : DaisyComponent
    {
        #region UXML Factory
        [UxmlAttribute]
        public string nodeId { get; set; }

        [UxmlAttribute]
        public string text { get; set; }

        [UxmlAttribute]
        public string icon { get; set; }

        [UxmlAttribute]
        public bool expanded { get; set; }

        [UxmlAttribute]
        public bool selected { get; set; }

        [UxmlAttribute]
        public int depth { get; set; }

        [UxmlAttribute]
        public bool showExpandButton { get; set; } = true;
        #endregion

        #region Events
        public event Action<string> OnNodeClicked;
        public event Action<string> OnNodeSelected;
        public event Action<string> OnNodeDeselected;
        public event Action<string> OnNodeExpanded;
        public event Action<string> OnNodeCollapsed;
        public event Action<string> OnNodeToggled;
        public event Action<string, string> OnActionTriggered;
        #endregion

        #region Properties
        public string NodeId
        {
            get => nodeId;
            set
            {
                nodeId = value;
                name = $"tree-node-{value}";
            }
        }

        public string Text
        {
            get => text;
            set
            {
                text = value;
                UpdateText();
            }
        }

        public string Icon
        {
            get => icon;
            set
            {
                icon = value;
                UpdateIcon();
            }
        }

        public bool Expanded
        {
            get => expanded;
            set
            {
                expanded = value;
                UpdateExpansion();
            }
        }

        public bool Selected
        {
            get => selected;
            set
            {
                selected = value;
                UpdateSelection();
            }
        }

        public int Depth
        {
            get => depth;
            set
            {
                depth = value;
                UpdateDepth();
            }
        }

        public bool ShowExpandButton
        {
            get => showExpandButton;
            set
            {
                showExpandButton = value;
                UpdateExpandButton();
            }
        }

        public DaisyTreeData Data { get; set; }
        public List<DaisyTreeAction> Actions { get; set; } = new List<DaisyTreeAction>();
        public List<DaisyTreeNode> ChildNodes { get; set; } = new List<DaisyTreeNode>();
        public List<DaisyTreeItem> ChildItems { get; set; } = new List<DaisyTreeItem>();
        public bool HasChildren => ChildNodes.Count > 0 || ChildItems.Count > 0;
        #endregion

        #region UI Elements
        private VisualElement _container;
        private VisualElement _header;
        private VisualElement _content;
        private VisualElement _indentContainer;
        private Button _expandButton;
        private Label _expandIcon;
        private Label _iconLabel;
        private Label _textLabel;
        private VisualElement _actionsContainer;
        private Button _mainButton;
        private VisualElement _childrenContainer;
        #endregion

        #region Template and Styling
        protected override string TemplatePath => "DaisyUI/Components/Navigation/Tree/DaisyTreeNode";
        #endregion

        #region Constructors
        public DaisyTreeNode() : base(DaisyUtilities.ComponentTypes.TreeNode)
        {
            ComponentType = "tree-node";
            InitializeComponent();
        }

        public DaisyTreeNode(string id, string text, string icon = null) : this()
        {
            NodeId = id;
            Text = text;
            Icon = icon;
        }

        public DaisyTreeNode(DaisyTreeData data) : this()
        {
            Data = data;
            NodeId = data.Id;
            Text = data.Text;
            Icon = data.Icon;
            Expanded = data.IsExpanded;
            Selected = data.IsSelected;
            Actions = data.Actions;
        }
        #endregion

        #region Factory Methods
        public static DaisyTreeNode Create(string id, string text, string icon = null)
        {
            return new DaisyTreeNode(id, text, icon);
        }

        public static DaisyTreeNode Create(DaisyTreeData data)
        {
            return new DaisyTreeNode(data);
        }
        #endregion

        #region Initialization
        private void InitializeComponent()
        {
            AddToClassList("daisy-tree-node");
        }

        protected override void OnInitialize()
        {
            base.OnInitialize();
            CacheElements();
            UpdateDisplay();
        }

        protected override void SetupEventHandlers()
        {
            base.SetupEventHandlers();

            if (_expandButton != null)
            {
                _expandButton.clicked += OnExpandButtonClicked;
            }

            if (_mainButton != null)
            {
                _mainButton.clicked += OnMainButtonClicked;
            }

            this.RegisterCallback<KeyDownEvent>(OnKeyDown);
            this.RegisterCallback<FocusInEvent>(OnFocusIn);
            this.RegisterCallback<FocusOutEvent>(OnFocusOut);
        }

        private void CacheElements()
        {
            _container = this.Q("container");
            _header = this.Q("header");
            _content = this.Q("content");
            _indentContainer = this.Q("indent-container");
            _expandButton = this.Q<Button>("expand-button");
            _expandIcon = this.Q<Label>("expand-icon");
            _iconLabel = this.Q<Label>("icon");
            _textLabel = this.Q<Label>("text");
            _actionsContainer = this.Q("actions-container");
            _mainButton = this.Q<Button>("main-button");
            _childrenContainer = this.Q("children-container");
        }

        #endregion

        #region Event Handlers
        private void OnExpandButtonClicked()
        {
            Toggle();
        }

        private void OnMainButtonClicked()
        {
            OnNodeClicked?.Invoke(NodeId);

            if (!Selected)
            {
                SetSelected(true);
            }
        }

        private void OnKeyDown(KeyDownEvent evt)
        {
            switch (evt.keyCode)
            {
                case KeyCode.Return:
                case KeyCode.KeypadEnter:
                case KeyCode.Space:
                    OnMainButtonClicked();
                    evt.StopPropagation();
                    break;

                case KeyCode.RightArrow:
                    if (!Expanded && HasChildren)
                    {
                        Expand();
                    }
                    evt.StopPropagation();
                    break;

                case KeyCode.LeftArrow:
                    if (Expanded)
                    {
                        Collapse();
                    }
                    evt.StopPropagation();
                    break;
            }
        }

        private void OnFocusIn(FocusInEvent evt)
        {
            AddToClassList("focus");
        }

        private void OnFocusOut(FocusOutEvent evt)
        {
            RemoveFromClassList("focus");
        }

        private void OnActionButtonClicked(string actionId)
        {
            OnActionTriggered?.Invoke(NodeId, actionId);
            Data?.TriggerAction(actionId);
        }
        #endregion

        #region Display Updates
        private void UpdateDisplay()
        {
            UpdateText();
            UpdateIcon();
            UpdateSelection();
            UpdateExpansion();
            UpdateDepth();
            UpdateExpandButton();
            UpdateActions();
        }

        private void UpdateText()
        {
            if (_textLabel != null && !string.IsNullOrEmpty(Text))
            {
                _textLabel.text = Text;
                _textLabel.style.display = DisplayStyle.Flex;
            }
            else if (_textLabel != null)
            {
                _textLabel.style.display = DisplayStyle.None;
            }
        }

        private void UpdateIcon()
        {
            if (_iconLabel != null && !string.IsNullOrEmpty(Icon))
            {
                _iconLabel.text = Icon;
                _iconLabel.style.display = DisplayStyle.Flex;
                _iconLabel.AddToClassList("daisy-icon");
            }
            else if (_iconLabel != null)
            {
                _iconLabel.style.display = DisplayStyle.None;
            }
        }

        private void UpdateSelection()
        {
            if (Selected)
            {
                AddToClassList("selected");
                RemoveFromClassList("deselected");
            }
            else
            {
                RemoveFromClassList("selected");
                AddToClassList("deselected");
            }
        }

        private void UpdateExpansion()
        {
            if (Expanded)
            {
                AddToClassList("expanded");
                RemoveFromClassList("collapsed");

                if (_childrenContainer != null)
                {
                    _childrenContainer.style.display = DisplayStyle.Flex;
                }

                if (_expandIcon != null)
                {
                    _expandIcon.text = "▼";
                    _expandIcon.RemoveFromClassList("collapsed");
                    _expandIcon.AddToClassList("expanded");
                }
            }
            else
            {
                RemoveFromClassList("expanded");
                AddToClassList("collapsed");

                if (_childrenContainer != null)
                {
                    _childrenContainer.style.display = DisplayStyle.None;
                }

                if (_expandIcon != null)
                {
                    _expandIcon.text = "▶";
                    _expandIcon.RemoveFromClassList("expanded");
                    _expandIcon.AddToClassList("collapsed");
                }
            }
        }

        private void UpdateDepth()
        {
            if (_indentContainer != null)
            {
                _indentContainer.Clear();

                for (int i = 0; i < Depth; i++)
                {
                    var indent = new VisualElement();
                    indent.AddToClassList("tree-indent");
                    _indentContainer.Add(indent);
                }
            }

            // Update depth CSS class
            RemoveFromClassList("depth-0");
            RemoveFromClassList("depth-1");
            RemoveFromClassList("depth-2");
            RemoveFromClassList("depth-3");
            RemoveFromClassList("depth-4");
            RemoveFromClassList("depth-5");

            AddToClassList($"depth-{Math.Min(Depth, 5)}");

            // Update children depth
            foreach (var child in ChildNodes)
            {
                child.SetDepth(Depth + 1);
            }

            foreach (var child in ChildItems)
            {
                child.SetDepth(Depth + 1);
            }
        }

        private void UpdateExpandButton()
        {
            if (_expandButton != null)
            {
                _expandButton.style.display = ShowExpandButton && HasChildren ? DisplayStyle.Flex : DisplayStyle.None;
            }
        }

        private void UpdateActions()
        {
            if (_actionsContainer == null) return;

            _actionsContainer.Clear();

            foreach (var action in Actions)
            {
                if (!action.IsVisible) continue;

                var actionButton = new Button();
                actionButton.name = $"action-{action.Id}";
                actionButton.AddToClassList("tree-action-button");

                if (!string.IsNullOrEmpty(action.Icon))
                {
                    actionButton.text = action.Icon;
                    actionButton.AddToClassList("daisy-icon");
                }

                if (!string.IsNullOrEmpty(action.Tooltip))
                {
                    actionButton.tooltip = action.Tooltip;
                }

                actionButton.SetEnabled(action.IsEnabled);
                actionButton.clicked += () => OnActionButtonClicked(action.Id);

                _actionsContainer.Add(actionButton);
            }
        }
        #endregion

        #region Children Management
        public DaisyTreeNode AddChildNode(string id, string text, string icon = null)
        {
            var childNode = new DaisyTreeNode(id, text, icon);
            AddChildNode(childNode);
            return childNode;
        }

        public DaisyTreeNode AddChildNode(DaisyTreeNode child)
        {
            if (child == null) return null;

            child.SetDepth(Depth + 1);
            ChildNodes.Add(child);

            if (_childrenContainer != null)
            {
                _childrenContainer.Add(child);
            }

            UpdateExpandButton();
            return child;
        }

        public DaisyTreeItem AddChildItem(string id, string text, string icon = null)
        {
            var childItem = new DaisyTreeItem(id, text, icon);
            AddChildItem(childItem);
            return childItem;
        }

        public DaisyTreeItem AddChildItem(DaisyTreeItem child)
        {
            if (child == null) return null;

            child.SetDepth(Depth + 1);
            ChildItems.Add(child);

            if (_childrenContainer != null)
            {
                _childrenContainer.Add(child);
            }

            UpdateExpandButton();
            return child;
        }

        public void RemoveChildNode(string id)
        {
            var child = ChildNodes.FirstOrDefault(c => c.NodeId == id);
            if (child != null)
            {
                RemoveChildNode(child);
            }
        }

        public void RemoveChildNode(DaisyTreeNode child)
        {
            if (child != null && ChildNodes.Contains(child))
            {
                ChildNodes.Remove(child);
                child.RemoveFromHierarchy();
                UpdateExpandButton();
            }
        }

        public void RemoveChildItem(string id)
        {
            var child = ChildItems.FirstOrDefault(c => c.ItemId == id);
            if (child != null)
            {
                RemoveChildItem(child);
            }
        }

        public void RemoveChildItem(DaisyTreeItem child)
        {
            if (child != null && ChildItems.Contains(child))
            {
                ChildItems.Remove(child);
                child.RemoveFromHierarchy();
                UpdateExpandButton();
            }
        }

        public void ClearChildren()
        {
            foreach (var child in ChildNodes)
            {
                child.RemoveFromHierarchy();
            }
            ChildNodes.Clear();

            foreach (var child in ChildItems)
            {
                child.RemoveFromHierarchy();
            }
            ChildItems.Clear();

            if (_childrenContainer != null)
            {
                _childrenContainer.Clear();
            }

            UpdateExpandButton();
        }

        public DaisyTreeNode FindChildNode(string id)
        {
            return ChildNodes.FirstOrDefault(c => c.NodeId == id);
        }

        public DaisyTreeItem FindChildItem(string id)
        {
            return ChildItems.FirstOrDefault(c => c.ItemId == id);
        }

        public DaisyTreeNode FindDescendantNode(string id)
        {
            foreach (var child in ChildNodes)
            {
                if (child.NodeId == id)
                    return child;

                var descendant = child.FindDescendantNode(id);
                if (descendant != null)
                    return descendant;
            }
            return null;
        }

        public DaisyTreeItem FindDescendantItem(string id)
        {
            foreach (var item in ChildItems)
            {
                if (item.ItemId == id)
                    return item;
            }

            foreach (var child in ChildNodes)
            {
                var descendant = child.FindDescendantItem(id);
                if (descendant != null)
                    return descendant;
            }
            return null;
        }
        #endregion

        #region Expansion Methods
        public DaisyTreeNode Expand()
        {
            if (!Expanded)
            {
                Expanded = true;
                OnNodeExpanded?.Invoke(NodeId);
                OnNodeToggled?.Invoke(NodeId);
                Data?.Expand();
            }
            return this;
        }

        public DaisyTreeNode Collapse()
        {
            if (Expanded)
            {
                Expanded = false;
                OnNodeCollapsed?.Invoke(NodeId);
                OnNodeToggled?.Invoke(NodeId);
                Data?.Collapse();
            }
            return this;
        }

        public DaisyTreeNode Toggle()
        {
            if (Expanded)
                Collapse();
            else
                Expand();
            return this;
        }

        public DaisyTreeNode ExpandAll()
        {
            Expand();

            foreach (var child in ChildNodes)
            {
                child.ExpandAll();
            }

            return this;
        }

        public DaisyTreeNode CollapseAll()
        {
            Collapse();

            foreach (var child in ChildNodes)
            {
                child.CollapseAll();
            }

            return this;
        }
        #endregion

        #region Selection Methods
        public DaisyTreeNode SetSelected(bool selected)
        {
            if (Selected != selected)
            {
                Selected = selected;

                if (selected)
                {
                    OnNodeSelected?.Invoke(NodeId);
                }
                else
                {
                    OnNodeDeselected?.Invoke(NodeId);
                }
            }

            return this;
        }
        #endregion

        #region Public Methods
        public DaisyTreeNode SetText(string text)
        {
            Text = text;
            return this;
        }

        public DaisyTreeNode SetIcon(string icon)
        {
            Icon = icon;
            return this;
        }

        public DaisyTreeNode SetDepth(int depth)
        {
            Depth = depth;
            return this;
        }

        public DaisyTreeNode SetShowExpandButton(bool show)
        {
            ShowExpandButton = show;
            return this;
        }

        public DaisyTreeNode AddAction(string actionId, string tooltip = null, string icon = null)
        {
            var action = new DaisyTreeAction(actionId, tooltip, icon);
            Actions.Add(action);
            UpdateActions();
            return this;
        }

        public DaisyTreeNode AddAction(DaisyTreeAction action)
        {
            if (action != null)
            {
                Actions.Add(action);
                UpdateActions();
            }
            return this;
        }

        public DaisyTreeNode RemoveAction(string actionId)
        {
            var action = Actions.Find(a => a.Id == actionId);
            if (action != null)
            {
                Actions.Remove(action);
                UpdateActions();
            }
            return this;
        }

        public DaisyTreeNode ClearActions()
        {
            Actions.Clear();
            UpdateActions();
            return this;
        }

        public DaisyTreeNode OnClick(Action<string> callback)
        {
            OnNodeClicked += callback;
            return this;
        }

        public DaisyTreeNode OnSelected(Action<string> callback)
        {
            OnNodeSelected += callback;
            return this;
        }

        public DaisyTreeNode OnDeselected(Action<string> callback)
        {
            OnNodeDeselected += callback;
            return this;
        }

        public DaisyTreeNode OnExpanded(Action<string> callback)
        {
            OnNodeExpanded += callback;
            return this;
        }

        public DaisyTreeNode OnCollapsed(Action<string> callback)
        {
            OnNodeCollapsed += callback;
            return this;
        }

        public DaisyTreeNode OnToggled(Action<string> callback)
        {
            OnNodeToggled += callback;
            return this;
        }

        public DaisyTreeNode OnAction(Action<string, string> callback)
        {
            OnActionTriggered += callback;
            return this;
        }

        public DaisyTreeNode Focus()
        {
            _mainButton?.Focus();
            return this;
        }

        public DaisyTreeNode Blur()
        {
            _mainButton?.Blur();
            return this;
        }
        #endregion

        #region Theme and Styling
        public DaisyTreeNode SetTheme(DaisyTheme theme)
        {
            Theme = theme;
            return this;
        }

        public DaisyTreeNode SetSize(string size)
        {
            Size = size;
            return this;
        }

        public DaisyTreeNode SetVariant(string variant)
        {
            Variant = variant;
            return this;
        }

        public DaisyTreeNode SetCompact(bool compact = true)
        {
            SetModifier("compact", compact);
            return this;
        }

        public DaisyTreeNode SetBordered(bool bordered = true)
        {
            SetModifier("bordered", bordered);
            return this;
        }

        public DaisyTreeNode SetRounded(bool rounded = true)
        {
            SetModifier("rounded", rounded);
            return this;
        }
        #endregion

        #region Data Binding
        public DaisyTreeNode BindData(DaisyTreeData data)
        {
            if (data != null)
            {
                Data = data;
                NodeId = data.Id;
                Text = data.Text;
                Icon = data.Icon;
                Expanded = data.IsExpanded;
                Selected = data.IsSelected;
                Actions = data.Actions;

                // Subscribe to data events
                data.OnNodeExpanded += (id) => Expand();
                data.OnNodeCollapsed += (id) => Collapse();
                data.OnItemSelected += (id) => SetSelected(true);
                data.OnItemAction += (id, actionId) => OnActionTriggered?.Invoke(id, actionId);

                // Clear existing children
                ClearChildren();

                // Add children from data
                foreach (var childData in data.Children)
                {
                    if (childData.HasChildren)
                    {
                        var childNode = new DaisyTreeNode(childData);
                        AddChildNode(childNode);
                    }
                    else
                    {
                        var childItem = new DaisyTreeItem(childData);
                        AddChildItem(childItem);
                    }
                }

                UpdateDisplay();
            }

            return this;
        }

        public DaisyTreeNode UnbindData()
        {
            Data = null;
            return this;
        }
        #endregion

        #region Utility Methods
        public override void Reset()
        {
            base.Reset();

            NodeId = null;
            Text = null;
            Icon = null;
            Expanded = false;
            Selected = false;
            Depth = 0;
            ShowExpandButton = true;
            Actions.Clear();
            ClearChildren();
            Data = null;

            OnNodeClicked = null;
            OnNodeSelected = null;
            OnNodeDeselected = null;
            OnNodeExpanded = null;
            OnNodeCollapsed = null;
            OnNodeToggled = null;
            OnActionTriggered = null;
        }

        public override string ToString()
        {
            return $"DaisyTreeNode(Id: {NodeId}, Text: {Text}, Children: {ChildNodes.Count + ChildItems.Count})";
        }
        #endregion
    }
}