/* DaisyUI 工具类样式 */

/* ========================================
   显示工具类
   ======================================== */

.daisy-block { display: flex; }
.daisy-inline-block { display: none; } /* Unity UI Toolkit 不支持 inline-block */
.daisy-inline { display: none; } /* Unity UI Toolkit 不支持 inline */
.daisy-flex { display: flex; }
.daisy-none { display: none; }
.daisy-hidden { display: none; }
.daisy-visible { display: flex; }

/* ========================================
   Flexbox 工具类
   ======================================== */

/* Flex Direction */
.daisy-flex-row { flex-direction: row; }
.daisy-flex-row-reverse { flex-direction: row-reverse; }
.daisy-flex-col { flex-direction: column; }
.daisy-flex-col-reverse { flex-direction: column-reverse; }

/* Flex Wrap */
.daisy-flex-wrap { flex-wrap: wrap; }
.daisy-flex-wrap-reverse { flex-wrap: wrap-reverse; }
.daisy-flex-nowrap { flex-wrap: nowrap; }

/* Align Items */
.daisy-items-start { align-items: flex-start; }
.daisy-items-end { align-items: flex-end; }
.daisy-items-center { align-items: center; }
.daisy-items-baseline { align-items: baseline; }
.daisy-items-stretch { align-items: stretch; }

/* Justify Content */
.daisy-justify-start { justify-content: flex-start; }
.daisy-justify-end { justify-content: flex-end; }
.daisy-justify-center { justify-content: center; }
.daisy-justify-between { justify-content: space-between; }
.daisy-justify-around { justify-content: space-around; }
.daisy-justify-evenly { justify-content: space-evenly; }

/* Align Self */
.daisy-self-auto { align-self: auto; }
.daisy-self-start { align-self: flex-start; }
.daisy-self-end { align-self: flex-end; }
.daisy-self-center { align-self: center; }
.daisy-self-stretch { align-self: stretch; }

/* Flex Grow & Shrink */
.daisy-flex-1 { flex-grow: 1; flex-shrink: 1; }
.daisy-flex-auto { flex: 1 1 auto; }
.daisy-flex-initial { flex: 0 1 auto; }
.daisy-flex-none { flex: none; }
.daisy-flex-grow { flex-grow: 1; }
.daisy-flex-grow-0 { flex-grow: 0; }
.daisy-flex-shrink { flex-shrink: 1; }
.daisy-flex-shrink-0 { flex-shrink: 0; }

/* ========================================
   间距工具类
   ======================================== */

/* Margin */
.daisy-m-0 { margin: 0; }
.daisy-m-1 { margin: var(--spacing-1); }
.daisy-m-2 { margin: var(--spacing-2); }
.daisy-m-3 { margin: var(--spacing-3); }
.daisy-m-4 { margin: var(--spacing-4); }
.daisy-m-5 { margin: var(--spacing-5); }
.daisy-m-6 { margin: var(--spacing-6); }
.daisy-m-8 { margin: var(--spacing-8); }
.daisy-m-10 { margin: var(--spacing-10); }
.daisy-m-12 { margin: var(--spacing-12); }
.daisy-m-16 { margin: var(--spacing-16); }
.daisy-m-20 { margin: var(--spacing-20); }
.daisy-m-24 { margin: var(--spacing-24); }
.daisy-m-32 { margin: var(--spacing-32); }
.daisy-m-auto { margin: auto; }

/* Margin X (left + right) */
.daisy-mx-0 { margin-left: 0; margin-right: 0; }
.daisy-mx-1 { margin-left: var(--spacing-1); margin-right: var(--spacing-1); }
.daisy-mx-2 { margin-left: var(--spacing-2); margin-right: var(--spacing-2); }
.daisy-mx-3 { margin-left: var(--spacing-3); margin-right: var(--spacing-3); }
.daisy-mx-4 { margin-left: var(--spacing-4); margin-right: var(--spacing-4); }
.daisy-mx-5 { margin-left: var(--spacing-5); margin-right: var(--spacing-5); }
.daisy-mx-6 { margin-left: var(--spacing-6); margin-right: var(--spacing-6); }
.daisy-mx-8 { margin-left: var(--spacing-8); margin-right: var(--spacing-8); }
.daisy-mx-auto { margin-left: auto; margin-right: auto; }

/* Margin Y (top + bottom) */
.daisy-my-0 { margin-top: 0; margin-bottom: 0; }
.daisy-my-1 { margin-top: var(--spacing-1); margin-bottom: var(--spacing-1); }
.daisy-my-2 { margin-top: var(--spacing-2); margin-bottom: var(--spacing-2); }
.daisy-my-3 { margin-top: var(--spacing-3); margin-bottom: var(--spacing-3); }
.daisy-my-4 { margin-top: var(--spacing-4); margin-bottom: var(--spacing-4); }
.daisy-my-5 { margin-top: var(--spacing-5); margin-bottom: var(--spacing-5); }
.daisy-my-6 { margin-top: var(--spacing-6); margin-bottom: var(--spacing-6); }
.daisy-my-8 { margin-top: var(--spacing-8); margin-bottom: var(--spacing-8); }
.daisy-my-auto { margin-top: auto; margin-bottom: auto; }

/* Margin Top */
.daisy-mt-0 { margin-top: 0; }
.daisy-mt-1 { margin-top: var(--spacing-1); }
.daisy-mt-2 { margin-top: var(--spacing-2); }
.daisy-mt-3 { margin-top: var(--spacing-3); }
.daisy-mt-4 { margin-top: var(--spacing-4); }
.daisy-mt-5 { margin-top: var(--spacing-5); }
.daisy-mt-6 { margin-top: var(--spacing-6); }
.daisy-mt-8 { margin-top: var(--spacing-8); }
.daisy-mt-10 { margin-top: var(--spacing-10); }
.daisy-mt-12 { margin-top: var(--spacing-12); }
.daisy-mt-16 { margin-top: var(--spacing-16); }
.daisy-mt-20 { margin-top: var(--spacing-20); }
.daisy-mt-24 { margin-top: var(--spacing-24); }
.daisy-mt-32 { margin-top: var(--spacing-32); }
.daisy-mt-auto { margin-top: auto; }

/* Margin Right */
.daisy-mr-0 { margin-right: 0; }
.daisy-mr-1 { margin-right: var(--spacing-1); }
.daisy-mr-2 { margin-right: var(--spacing-2); }
.daisy-mr-3 { margin-right: var(--spacing-3); }
.daisy-mr-4 { margin-right: var(--spacing-4); }
.daisy-mr-5 { margin-right: var(--spacing-5); }
.daisy-mr-6 { margin-right: var(--spacing-6); }
.daisy-mr-8 { margin-right: var(--spacing-8); }
.daisy-mr-auto { margin-right: auto; }

/* Margin Bottom */
.daisy-mb-0 { margin-bottom: 0; }
.daisy-mb-1 { margin-bottom: var(--spacing-1); }
.daisy-mb-2 { margin-bottom: var(--spacing-2); }
.daisy-mb-3 { margin-bottom: var(--spacing-3); }
.daisy-mb-4 { margin-bottom: var(--spacing-4); }
.daisy-mb-5 { margin-bottom: var(--spacing-5); }
.daisy-mb-6 { margin-bottom: var(--spacing-6); }
.daisy-mb-8 { margin-bottom: var(--spacing-8); }
.daisy-mb-auto { margin-bottom: auto; }

/* Margin Left */
.daisy-ml-0 { margin-left: 0; }
.daisy-ml-1 { margin-left: var(--spacing-1); }
.daisy-ml-2 { margin-left: var(--spacing-2); }
.daisy-ml-3 { margin-left: var(--spacing-3); }
.daisy-ml-4 { margin-left: var(--spacing-4); }
.daisy-ml-5 { margin-left: var(--spacing-5); }
.daisy-ml-6 { margin-left: var(--spacing-6); }
.daisy-ml-8 { margin-left: var(--spacing-8); }
.daisy-ml-auto { margin-left: auto; }

/* Padding */
.daisy-p-0 { padding: 0; }
.daisy-p-1 { padding: var(--spacing-1); }
.daisy-p-2 { padding: var(--spacing-2); }
.daisy-p-3 { padding: var(--spacing-3); }
.daisy-p-4 { padding: var(--spacing-4); }
.daisy-p-5 { padding: var(--spacing-5); }
.daisy-p-6 { padding: var(--spacing-6); }
.daisy-p-8 { padding: var(--spacing-8); }
.daisy-p-10 { padding: var(--spacing-10); }
.daisy-p-12 { padding: var(--spacing-12); }
.daisy-p-16 { padding: var(--spacing-16); }
.daisy-p-20 { padding: var(--spacing-20); }
.daisy-p-24 { padding: var(--spacing-24); }
.daisy-p-32 { padding: var(--spacing-32); }

/* Padding X (left + right) */
.daisy-px-0 { padding-left: 0; padding-right: 0; }
.daisy-px-1 { padding-left: var(--spacing-1); padding-right: var(--spacing-1); }
.daisy-px-2 { padding-left: var(--spacing-2); padding-right: var(--spacing-2); }
.daisy-px-3 { padding-left: var(--spacing-3); padding-right: var(--spacing-3); }
.daisy-px-4 { padding-left: var(--spacing-4); padding-right: var(--spacing-4); }
.daisy-px-5 { padding-left: var(--spacing-5); padding-right: var(--spacing-5); }
.daisy-px-6 { padding-left: var(--spacing-6); padding-right: var(--spacing-6); }
.daisy-px-8 { padding-left: var(--spacing-8); padding-right: var(--spacing-8); }

/* Padding Y (top + bottom) */
.daisy-py-0 { padding-top: 0; padding-bottom: 0; }
.daisy-py-1 { padding-top: var(--spacing-1); padding-bottom: var(--spacing-1); }
.daisy-py-2 { padding-top: var(--spacing-2); padding-bottom: var(--spacing-2); }
.daisy-py-3 { padding-top: var(--spacing-3); padding-bottom: var(--spacing-3); }
.daisy-py-4 { padding-top: var(--spacing-4); padding-bottom: var(--spacing-4); }
.daisy-py-5 { padding-top: var(--spacing-5); padding-bottom: var(--spacing-5); }
.daisy-py-6 { padding-top: var(--spacing-6); padding-bottom: var(--spacing-6); }
.daisy-py-8 { padding-top: var(--spacing-8); padding-bottom: var(--spacing-8); }

/* Gap (for flex containers) */
.daisy-gap-0 { gap: 0; }
.daisy-gap-1 { gap: var(--spacing-1); }
.daisy-gap-2 { gap: var(--spacing-2); }
.daisy-gap-3 { gap: var(--spacing-3); }
.daisy-gap-4 { gap: var(--spacing-4); }
.daisy-gap-5 { gap: var(--spacing-5); }
.daisy-gap-6 { gap: var(--spacing-6); }
.daisy-gap-8 { gap: var(--spacing-8); }
.daisy-gap-10 { gap: var(--spacing-10); }
.daisy-gap-12 { gap: var(--spacing-12); }
.daisy-gap-16 { gap: var(--spacing-16); }

/* ========================================
   尺寸工具类
   ======================================== */

/* Width */
.daisy-w-auto { width: auto; }
.daisy-w-full { width: 100%; }
.daisy-w-screen { width: 100%; }
.daisy-w-min { width: min-content; }
.daisy-w-max { width: max-content; }
.daisy-w-fit { width: fit-content; }

.daisy-w-0 { width: 0; }
.daisy-w-1 { width: var(--spacing-1); }
.daisy-w-2 { width: var(--spacing-2); }
.daisy-w-3 { width: var(--spacing-3); }
.daisy-w-4 { width: var(--spacing-4); }
.daisy-w-5 { width: var(--spacing-5); }
.daisy-w-6 { width: var(--spacing-6); }
.daisy-w-8 { width: var(--spacing-8); }
.daisy-w-10 { width: var(--spacing-10); }
.daisy-w-12 { width: var(--spacing-12); }
.daisy-w-16 { width: var(--spacing-16); }
.daisy-w-20 { width: var(--spacing-20); }
.daisy-w-24 { width: var(--spacing-24); }
.daisy-w-32 { width: var(--spacing-32); }

/* Width Percentages */
.daisy-w-1-12 { width: 8.333333%; }
.daisy-w-2-12 { width: 16.666667%; }
.daisy-w-3-12 { width: 25%; }
.daisy-w-4-12 { width: 33.333333%; }
.daisy-w-5-12 { width: 41.666667%; }
.daisy-w-6-12 { width: 50%; }
.daisy-w-7-12 { width: 58.333333%; }
.daisy-w-8-12 { width: 66.666667%; }
.daisy-w-9-12 { width: 75%; }
.daisy-w-10-12 { width: 83.333333%; }
.daisy-w-11-12 { width: 91.666667%; }

/* Height */
.daisy-h-auto { height: auto; }
.daisy-h-full { height: 100%; }
.daisy-h-screen { height: 100%; }
.daisy-h-min { height: min-content; }
.daisy-h-max { height: max-content; }
.daisy-h-fit { height: fit-content; }

.daisy-h-0 { height: 0; }
.daisy-h-1 { height: var(--spacing-1); }
.daisy-h-2 { height: var(--spacing-2); }
.daisy-h-3 { height: var(--spacing-3); }
.daisy-h-4 { height: var(--spacing-4); }
.daisy-h-5 { height: var(--spacing-5); }
.daisy-h-6 { height: var(--spacing-6); }
.daisy-h-8 { height: var(--spacing-8); }
.daisy-h-10 { height: var(--spacing-10); }
.daisy-h-12 { height: var(--spacing-12); }
.daisy-h-16 { height: var(--spacing-16); }
.daisy-h-20 { height: var(--spacing-20); }
.daisy-h-24 { height: var(--spacing-24); }
.daisy-h-32 { height: var(--spacing-32); }

/* Min/Max Width */
.daisy-min-w-0 { min-width: 0; }
.daisy-min-w-full { min-width: 100%; }
.daisy-max-w-none { max-width: none; }
.daisy-max-w-full { max-width: 100%; }
.daisy-max-w-screen { max-width: 100%; }

/* Min/Max Height */
.daisy-min-h-0 { min-height: 0; }
.daisy-min-h-full { min-height: 100%; }
.daisy-max-h-full { max-height: 100%; }
.daisy-max-h-screen { max-height: 100%; }

/* ========================================
   文本工具类
   ======================================== */

/* Font Size */
.daisy-text-xs { font-size: var(--font-size-xs); }
.daisy-text-sm { font-size: var(--font-size-sm); }
.daisy-text-base { font-size: var(--font-size-base); }
.daisy-text-lg { font-size: var(--font-size-lg); }
.daisy-text-xl { font-size: var(--font-size-xl); }
.daisy-text-3xl { font-size: var(--font-size-3xl); }
.daisy-text-4xl { font-size: var(--font-size-4xl); }
.daisy-text-5xl { font-size: var(--font-size-5xl); }
.daisy-text-6xl { font-size: var(--font-size-6xl); }

/* Font Weight */
.daisy-font-thin { -unity-font-style: normal; }
.daisy-font-light { -unity-font-style: normal; }
.daisy-font-normal { -unity-font-style: normal; }
.daisy-font-medium { -unity-font-style: normal; }
.daisy-font-semibold { -unity-font-style: bold; }
.daisy-font-bold { -unity-font-style: bold; }
.daisy-font-extrabold { -unity-font-style: bold; }
.daisy-font-black { -unity-font-style: bold; }

/* Text Align */
.daisy-text-left { -unity-text-align: upper-left; }
.daisy-text-center { -unity-text-align: upper-center; }
.daisy-text-right { -unity-text-align: upper-right; }

/* Text Color */
.daisy-text-primary { color: var(--primary); }
.daisy-text-secondary { color: var(--secondary); }
.daisy-text-accent { color: var(--accent); }
.daisy-text-neutral { color: var(--neutral); }
.daisy-text-base-content { color: var(--base-content); }
.daisy-text-info { color: var(--info); }
.daisy-text-success { color: var(--success); }
.daisy-text-warning { color: var(--warning); }
.daisy-text-error { color: var(--error); }

/* White Space */
.daisy-whitespace-normal { white-space: normal; }
.daisy-whitespace-nowrap { white-space: nowrap; }

/* Text Overflow */
.daisy-truncate { 
    overflow: hidden; 
    -unity-text-overflow-position: right;
}

/* ========================================
   边框工具类
   ======================================== */

/* Border Width */
.daisy-border { border-width: var(--border-width); }
.daisy-border-0 { border-width: 0; }
.daisy-border-2 { border-width: var(--border-width-2); }
.daisy-border-4 { border-width: var(--border-width-4); }

/* Border Color */
.daisy-border-transparent { border-color: transparent; }
.daisy-border-current { border-color: currentColor; }
.daisy-border-primary { border-color: var(--primary); }
.daisy-border-secondary { border-color: var(--secondary); }
.daisy-border-accent { border-color: var(--accent); }
.daisy-border-neutral { border-color: var(--neutral); }
.daisy-border-base-300 { border-color: var(--base-300); }

/* Border Radius */
.daisy-rounded-none { border-radius: 0; }
.daisy-rounded-sm { border-radius: var(--border-radius-sm); }
.daisy-rounded { border-radius: var(--border-radius); }
.daisy-rounded-lg { border-radius: var(--border-radius-lg); }
.daisy-rounded-xl { border-radius: var(--border-radius-xl); }
.daisy-rounded-full { border-radius: var(--border-radius-full); }

/* ========================================
   背景工具类
   ======================================== */

/* Background Color */
.daisy-bg-transparent { background-color: transparent; }
.daisy-bg-current { background-color: currentColor; }
.daisy-bg-primary { background-color: var(--primary); }
.daisy-bg-secondary { background-color: var(--secondary); }
.daisy-bg-accent { background-color: var(--accent); }
.daisy-bg-neutral { background-color: var(--neutral); }
.daisy-bg-base-100 { background-color: var(--base-100); }
.daisy-bg-base-200 { background-color: var(--base-200); }
.daisy-bg-base-300 { background-color: var(--base-300); }
.daisy-bg-info { background-color: var(--info); }
.daisy-bg-success { background-color: var(--success); }
.daisy-bg-warning { background-color: var(--warning); }
.daisy-bg-error { background-color: var(--error); }

/* ========================================
   阴影工具类
   ======================================== */

.daisy-shadow-none { box-shadow: none; }
.daisy-shadow-sm { box-shadow: var(--shadow-sm); }
.daisy-shadow { box-shadow: var(--shadow); }
.daisy-shadow-md { box-shadow: var(--shadow-md); }
.daisy-shadow-lg { box-shadow: var(--shadow-lg); }
.daisy-shadow-xl { box-shadow: var(--shadow-xl); }
.daisy-shadow-inner { box-shadow: var(--shadow-inner); }

/* ========================================
   位置工具类
   ======================================== */

.daisy-static { position: relative; }
.daisy-fixed { position: absolute; }
.daisy-absolute { position: absolute; }
.daisy-relative { position: relative; }

/* Top/Right/Bottom/Left */
.daisy-top-0 { top: 0; }
.daisy-right-0 { right: 0; }
.daisy-bottom-0 { bottom: 0; }
.daisy-left-0 { left: 0; }
.daisy-top-auto { top: auto; }
.daisy-right-auto { right: auto; }
.daisy-bottom-auto { bottom: auto; }
.daisy-left-auto { left: auto; }

/* Z-Index */
.daisy-z-0 { z-index: 0; }
.daisy-z-10 { z-index: 10; }
.daisy-z-20 { z-index: 20; }
.daisy-z-30 { z-index: 30; }
.daisy-z-40 { z-index: 40; }
.daisy-z-50 { z-index: 50; }
.daisy-z-auto { z-index: auto; }

/* ========================================
   过渡和动画工具类
   ======================================== */

/* Transition */
.daisy-transition-none { transition: none; }
.daisy-transition-all { 
    transition: all var(--transition-duration) var(--transition-easing); 
}
.daisy-transition { 
    transition: 
        background-color var(--transition-duration) var(--transition-easing),
        border-color var(--transition-duration) var(--transition-easing),
        color var(--transition-duration) var(--transition-easing); 
}

/* Transition Duration */
.daisy-duration-75 { transition-duration: 75ms; }
.daisy-duration-100 { transition-duration: 100ms; }
.daisy-duration-150 { transition-duration: 150ms; }
.daisy-duration-200 { transition-duration: 200ms; }
.daisy-duration-300 { transition-duration: 300ms; }
.daisy-duration-500 { transition-duration: 500ms; }
.daisy-duration-700 { transition-duration: 700ms; }
.daisy-duration-1000 { transition-duration: 1000ms; }

/* Transition Timing Function */
.daisy-ease-linear { transition-timing-function: linear; }
.daisy-ease-in { transition-timing-function: ease-in; }
.daisy-ease-out { transition-timing-function: ease-out; }
.daisy-ease-in-out { transition-timing-function: ease-in-out; }

/* ========================================
   交互状态工具类
   ======================================== */

/* Cursor */
.daisy-cursor-auto { cursor: auto; }
.daisy-cursor-default { cursor: default; }
.daisy-cursor-pointer { cursor: pointer; }
.daisy-cursor-wait { cursor: wait; }
.daisy-cursor-text { cursor: text; }
.daisy-cursor-move { cursor: move; }
.daisy-cursor-not-allowed { cursor: not-allowed; }

/* Pointer Events */
.daisy-pointer-events-none { pointer-events: none; }
.daisy-pointer-events-auto { pointer-events: auto; }

/* User Select */
.daisy-select-none { -unity-user-select: none; }
.daisy-select-text { -unity-user-select: text; }
.daisy-select-all { -unity-user-select: all; }
.daisy-select-auto { -unity-user-select: auto; }